from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    """用户基础模型"""

    username: str = Field(..., description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否为超级用户")


class UserCreate(UserBase):
    """用户创建模型"""

    password: str = Field(..., description="密码")


class UserUpdate(UserBase):
    """用户更新模型"""

    password: Optional[str] = Field(None, description="密码")


class User(UserBase):
    """返回给API的用户模型"""

    id: int = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用户登录模型"""

    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class Token(BaseModel):
    """令牌模型"""

    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")


class TokenPayload(BaseModel):
    """令牌载荷模型"""

    sub: Optional[str] = Field(None, description="主题（用户ID字符串）")
    exp: Optional[datetime] = Field(None, description="过期时间")
